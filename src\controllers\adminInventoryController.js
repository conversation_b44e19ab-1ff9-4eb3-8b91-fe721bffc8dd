const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取库存列表
 */
async function getInventoryList(req, res) {
  try {
    const { 
      page = 1, 
      limit = 10, 
      product_id = '', 
      status = 'all' 
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params = [];
    
    if (product_id) {
      whereClause += ' AND pi.product_id = ?';
      params.push(parseInt(product_id));
    }
    
    if (status && status !== 'all') {
      whereClause += ' AND pi.status = ?';
      params.push(status);
    }
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM product_inventory pi ${whereClause}`;
    const [countResult] = await db.execute(countSql, params);
    const total = countResult[0].total;
    
    // 获取库存列表
    const sql = `
      SELECT 
        pi.id, pi.product_id, pi.email_account, pi.email_password, pi.status, pi.created_at, pi.updated_at,
        p.name as product_name, p.price as product_price
      FROM product_inventory pi
      LEFT JOIN products p ON pi.product_id = p.id
      ${whereClause}
      ORDER BY pi.created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(limit), offset);
    const [inventory] = await db.execute(sql, params);

    // 计算分页信息
    const totalPages = Math.ceil(total / parseInt(limit));
    
    success(res, {
      inventory,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    }, '获取库存列表成功');

  } catch (err) {
    console.error('获取库存列表错误:', err);
    error(res, '获取库存列表失败', 500);
  }
}

/**
 * 添加库存
 */
async function addInventory(req, res) {
  try {
    const { product_id, email_account, email_password } = req.body;

    // 基础验证
    if (!product_id || !email_account || !email_password) {
      return error(res, '商品ID、邮箱账号和密码不能为空', 400);
    }

    // 检查商品是否存在
    const [products] = await db.execute(
      'SELECT id FROM products WHERE id = ?',
      [product_id]
    );

    if (products.length === 0) {
      return error(res, '商品不存在', 404);
    }

    // 检查邮箱账号是否已存在
    const [existingInventory] = await db.execute(
      'SELECT id FROM product_inventory WHERE email_account = ?',
      [email_account]
    );

    if (existingInventory.length > 0) {
      return error(res, '该邮箱账号已存在', 400);
    }

    // 添加库存
    const [result] = await db.execute(
      'INSERT INTO product_inventory (product_id, email_account, email_password) VALUES (?, ?, ?)',
      [product_id, email_account, email_password]
    );

    // 获取创建的库存信息
    const [newInventory] = await db.execute(
      `SELECT 
        pi.id, pi.product_id, pi.email_account, pi.email_password, pi.status, pi.created_at,
        p.name as product_name
      FROM product_inventory pi
      LEFT JOIN products p ON pi.product_id = p.id
      WHERE pi.id = ?`,
      [result.insertId]
    );

    success(res, {
      inventory: newInventory[0]
    }, '库存添加成功', 201);

  } catch (err) {
    console.error('添加库存错误:', err);
    error(res, '添加库存失败', 500);
  }
}

/**
 * 批量导入库存
 */
async function batchImportInventory(req, res) {
  try {
    const { product_id, inventory_list } = req.body;

    // 基础验证
    if (!product_id || !Array.isArray(inventory_list) || inventory_list.length === 0) {
      return error(res, '商品ID和库存列表不能为空', 400);
    }

    // 检查商品是否存在
    const [products] = await db.execute(
      'SELECT id FROM products WHERE id = ?',
      [product_id]
    );

    if (products.length === 0) {
      return error(res, '商品不存在', 404);
    }

    // 验证库存数据格式
    for (const item of inventory_list) {
      if (!item.email_account || !item.email_password) {
        return error(res, '库存数据格式错误，邮箱账号和密码不能为空', 400);
      }
    }

    // 检查重复的邮箱账号
    const emailAccounts = inventory_list.map(item => item.email_account);
    const [existingInventory] = await db.execute(
      `SELECT email_account FROM product_inventory WHERE email_account IN (${emailAccounts.map(() => '?').join(',')})`,
      emailAccounts
    );

    if (existingInventory.length > 0) {
      const duplicateEmails = existingInventory.map(item => item.email_account);
      return error(res, `以下邮箱账号已存在: ${duplicateEmails.join(', ')}`, 400);
    }

    // 批量插入库存
    const insertPromises = inventory_list.map(item => 
      db.execute(
        'INSERT INTO product_inventory (product_id, email_account, email_password) VALUES (?, ?, ?)',
        [product_id, item.email_account, item.email_password]
      )
    );

    await Promise.all(insertPromises);

    success(res, {
      imported_count: inventory_list.length
    }, `成功导入 ${inventory_list.length} 条库存记录`, 201);

  } catch (err) {
    console.error('批量导入库存错误:', err);
    error(res, '批量导入库存失败', 500);
  }
}

/**
 * 更新库存状态
 */
async function updateInventoryStatus(req, res) {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status || !['available', 'sold', 'reserved'].includes(status)) {
      return error(res, '状态参数无效', 400);
    }

    // 检查库存是否存在
    const [existingInventory] = await db.execute(
      'SELECT id FROM product_inventory WHERE id = ?',
      [id]
    );

    if (existingInventory.length === 0) {
      return error(res, '库存记录不存在', 404);
    }

    // 更新状态
    await db.execute(
      'UPDATE product_inventory SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, id]
    );

    // 获取更新后的库存信息
    const [updatedInventory] = await db.execute(
      `SELECT 
        pi.id, pi.product_id, pi.email_account, pi.email_password, pi.status, pi.created_at, pi.updated_at,
        p.name as product_name
      FROM product_inventory pi
      LEFT JOIN products p ON pi.product_id = p.id
      WHERE pi.id = ?`,
      [id]
    );

    success(res, {
      inventory: updatedInventory[0]
    }, '库存状态更新成功');

  } catch (err) {
    console.error('更新库存状态错误:', err);
    error(res, '更新库存状态失败', 500);
  }
}

/**
 * 删除库存
 */
async function deleteInventory(req, res) {
  try {
    const { id } = req.params;

    // 检查库存是否存在
    const [existingInventory] = await db.execute(
      'SELECT id, status FROM product_inventory WHERE id = ?',
      [id]
    );

    if (existingInventory.length === 0) {
      return error(res, '库存记录不存在', 404);
    }

    // 检查是否已售出
    if (existingInventory[0].status === 'sold') {
      return error(res, '已售出的库存无法删除', 400);
    }

    // 删除库存
    await db.execute('DELETE FROM product_inventory WHERE id = ?', [id]);

    success(res, null, '库存删除成功');

  } catch (err) {
    console.error('删除库存错误:', err);
    error(res, '删除库存失败', 500);
  }
}

/**
 * 获取库存统计
 */
async function getInventoryStats(req, res) {
  try {
    const { product_id } = req.query;

    let whereClause = '';
    const params = [];

    if (product_id) {
      whereClause = 'WHERE product_id = ?';
      params.push(parseInt(product_id));
    }

    // 获取库存统计
    const [stats] = await db.execute(
      `SELECT 
        status,
        COUNT(*) as count
      FROM product_inventory 
      ${whereClause}
      GROUP BY status`,
      params
    );

    // 格式化统计数据
    const statsMap = {
      available: 0,
      sold: 0,
      reserved: 0
    };

    stats.forEach(stat => {
      statsMap[stat.status] = stat.count;
    });

    success(res, {
      stats: statsMap,
      total: statsMap.available + statsMap.sold + statsMap.reserved
    }, '获取库存统计成功');

  } catch (err) {
    console.error('获取库存统计错误:', err);
    error(res, '获取库存统计失败', 500);
  }
}

module.exports = {
  getInventoryList,
  addInventory,
  batchImportInventory,
  updateInventoryStatus,
  deleteInventory,
  getInventoryStats
};
