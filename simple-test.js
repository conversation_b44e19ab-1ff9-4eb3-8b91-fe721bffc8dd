/**
 * 简单的第一步修复验证测试
 */

const request = require('supertest');
const app = require('./src/app');

async function testStep1Fix() {
  console.log('开始第一步修复验证测试...\n');

  try {
    // 1. 测试管理员登录
    console.log('1. 测试管理员登录...');
    const loginResponse = await request(app)
      .post('/api/admin/auth/login')
      .send({
        username: 'admin',
        password: 'admin123'
      });

    if (loginResponse.status !== 200) {
      console.log('❌ 管理员登录失败:', loginResponse.body);
      return;
    }
    console.log('✅ 管理员登录成功');

    const adminToken = loginResponse.body.data.token;

    // 2. 测试获取用户列表（无参数）
    console.log('\n2. 测试获取用户列表（无参数）...');
    const usersResponse = await request(app)
      .get('/api/admin/users')
      .set('Authorization', `Bearer ${adminToken}`);

    if (usersResponse.status !== 200) {
      console.log('❌ 获取用户列表失败:', usersResponse.body);
      console.log('状态码:', usersResponse.status);
      return;
    }
    console.log('✅ 获取用户列表成功');

    // 3. 测试搜索用户（有参数）
    console.log('\n3. 测试搜索用户（有参数）...');
    const searchResponse = await request(app)
      .get('/api/admin/users?search=test')
      .set('Authorization', `Bearer ${adminToken}`);

    if (searchResponse.status !== 200) {
      console.log('❌ 搜索用户失败:', searchResponse.body);
      console.log('状态码:', searchResponse.status);
      return;
    }
    console.log('✅ 搜索用户成功');

    // 4. 测试角色筛选（有参数）
    console.log('\n4. 测试角色筛选（有参数）...');
    const roleResponse = await request(app)
      .get('/api/admin/users?role=customer')
      .set('Authorization', `Bearer ${adminToken}`);

    if (roleResponse.status !== 200) {
      console.log('❌ 角色筛选失败:', roleResponse.body);
      console.log('状态码:', roleResponse.status);
      return;
    }
    console.log('✅ 角色筛选成功');

    // 5. 测试仪表盘概览
    console.log('\n5. 测试仪表盘概览...');
    const dashboardResponse = await request(app)
      .get('/api/admin/dashboard')
      .set('Authorization', `Bearer ${adminToken}`);

    if (dashboardResponse.status !== 200) {
      console.log('❌ 仪表盘概览失败:', dashboardResponse.body);
      console.log('状态码:', dashboardResponse.status);
      return;
    }
    console.log('✅ 仪表盘概览成功');

    // 6. 测试图表数据
    console.log('\n6. 测试图表数据...');
    const chartResponse = await request(app)
      .get('/api/admin/dashboard/charts')
      .set('Authorization', `Bearer ${adminToken}`);

    if (chartResponse.status !== 200) {
      console.log('❌ 图表数据失败:', chartResponse.body);
      console.log('状态码:', chartResponse.status);
      return;
    }
    console.log('✅ 图表数据成功');

    console.log('\n🎉 第一步修复验证测试全部通过！');
    console.log('✅ 数据库结构正常');
    console.log('✅ 管理员认证正常');
    console.log('✅ SQL参数绑定问题已修复');
    console.log('✅ 路由引用问题已修复');

  } catch (error) {
    console.log('❌ 测试过程中发生错误:', error.message);
  }

  process.exit(0);
}

// 运行测试
testStep1Fix();
