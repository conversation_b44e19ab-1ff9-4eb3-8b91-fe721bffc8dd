const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取仪表盘概览数据
 */
async function getDashboardOverview(req, res) {
  try {
    // 获取基础统计数据
    const [userStats] = await db.execute('SELECT COUNT(*) as total_users FROM users');
    const [orderStats] = await db.execute('SELECT COUNT(*) as total_orders FROM orders');
    const [productStats] = await db.execute('SELECT COUNT(*) as total_products FROM products');
    const [inventoryStats] = await db.execute('SELECT COUNT(*) as total_inventory FROM product_inventory');

    // 获取今日数据
    const [todayOrders] = await db.execute(
      "SELECT COUNT(*) as today_orders, COALESCE(SUM(total_amount), 0) as today_sales FROM orders WHERE DATE(created_at) = CURDATE()"
    );

    // 获取待处理订单
    const [pendingOrders] = await db.execute(
      "SELECT COUNT(*) as pending_orders FROM orders WHERE status = 'pending_payment'"
    );

    // 获取库存预警
    const [lowStockProducts] = await db.execute(
      `SELECT COUNT(*) as low_stock_count 
       FROM products p 
       WHERE (SELECT COUNT(*) FROM product_inventory WHERE product_id = p.id AND status = 'available') < 5`
    );

    success(res, {
      total_users: userStats[0].total_users,
      total_orders: orderStats[0].total_orders,
      total_products: productStats[0].total_products,
      total_inventory: inventoryStats[0].total_inventory,
      today_orders: todayOrders[0].today_orders,
      today_sales: todayOrders[0].today_sales,
      pending_orders: pendingOrders[0].pending_orders,
      low_stock_count: lowStockProducts[0].low_stock_count
    }, '获取仪表盘数据成功');

  } catch (err) {
    console.error('获取仪表盘数据错误:', err);
    error(res, '获取仪表盘数据失败', 500);
  }
}

/**
 * 获取图表数据
 */
async function getChartData(req, res) {
  try {
    const { period = 'week' } = req.query;
    
    let dateFormat, intervalDays;
    switch (period) {
      case 'month':
        dateFormat = '%Y-%m-%d';
        intervalDays = 29;
        break;
      case 'week':
      default:
        dateFormat = '%Y-%m-%d';
        intervalDays = 6;
        break;
    }

    // 获取销售趋势数据
    const [salesTrend] = await db.execute(
      `SELECT
         DATE_FORMAT(created_at, ?) as date,
         COUNT(*) as order_count,
         COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END), 0) as sales_amount
       FROM orders
       WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
       GROUP BY DATE_FORMAT(created_at, ?)
       ORDER BY date ASC`,
      [dateFormat, intervalDays, dateFormat]
    );

    // 获取订单状态分布
    const [orderStatus] = await db.execute(
      `SELECT 
         status,
         COUNT(*) as count
       FROM orders 
       WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
       GROUP BY status`,
      [intervalDays]
    );

    success(res, {
      sales_trend: salesTrend,
      order_status: orderStatus
    }, '获取图表数据成功');

  } catch (err) {
    console.error('获取图表数据错误:', err);
    error(res, '获取图表数据失败', 500);
  }
}

/**
 * 获取商品销售排行
 */
async function getProductRanking(req, res) {
  try {
    const { limit = 10 } = req.query;

    const [ranking] = await db.execute(
      `SELECT
         p.id,
         p.name,
         p.price,
         COUNT(oi.id) as sales_count,
         SUM(oi.price * oi.quantity) as sales_amount,
         (SELECT COUNT(*) FROM product_inventory WHERE product_id = p.id AND status = 'available') as available_stock
       FROM products p
       LEFT JOIN order_items oi ON p.id = oi.product_id
       LEFT JOIN orders o ON oi.order_id = o.id AND o.status = 'completed'
       WHERE p.status = 'active'
       GROUP BY p.id, p.name, p.price
       ORDER BY sales_count DESC, sales_amount DESC
       LIMIT ?`,
      [parseInt(limit)]
    );

    success(res, {
      ranking
    }, '获取商品销售排行成功');

  } catch (err) {
    console.error('获取商品销售排行错误:', err);
    error(res, '获取商品销售排行失败', 500);
  }
}

/**
 * 获取库存预警信息
 */
async function getInventoryAlerts(req, res) {
  try {
    const threshold = 5; // 库存预警阈值

    const [alerts] = await db.execute(
      `SELECT 
         p.id,
         p.name,
         p.price,
         COUNT(pi.id) as available_stock
       FROM products p
       LEFT JOIN product_inventory pi ON p.id = pi.product_id AND pi.status = 'available'
       WHERE p.status = 'active'
       GROUP BY p.id, p.name, p.price
       HAVING available_stock < ?
       ORDER BY available_stock ASC`,
      [threshold]
    );

    success(res, {
      threshold,
      alerts,
      total_alerts: alerts.length
    }, '获取库存预警成功');

  } catch (err) {
    console.error('获取库存预警错误:', err);
    error(res, '获取库存预警失败', 500);
  }
}

/**
 * 获取用户统计信息
 */
async function getUserStats(req, res) {
  try {
    // 总用户数
    const [totalUsers] = await db.execute('SELECT COUNT(*) as total FROM users');
    
    // 今日注册用户
    const [todayRegisters] = await db.execute(
      'SELECT COUNT(*) as total FROM users WHERE DATE(created_at) = CURDATE()'
    );
    
    // 本月注册用户
    const [monthRegisters] = await db.execute(
      'SELECT COUNT(*) as total FROM users WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())'
    );
    
    // 活跃用户（有订单的用户）
    const [activeUsers] = await db.execute(
      'SELECT COUNT(DISTINCT user_id) as total FROM orders'
    );

    success(res, {
      total_users: totalUsers[0].total,
      today_registers: todayRegisters[0].total,
      month_registers: monthRegisters[0].total,
      active_users: activeUsers[0].total
    }, '获取用户统计成功');

  } catch (err) {
    console.error('获取用户统计错误:', err);
    error(res, '获取用户统计失败', 500);
  }
}

module.exports = {
  getDashboardOverview,
  getChartData,
  getProductRanking,
  getInventoryAlerts,
  getUserStats
};
