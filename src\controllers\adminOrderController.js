const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取订单列表
 */
async function getOrderList(req, res) {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status = 'all',
      user_id = '',
      order_number = ''
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params = [];
    
    if (status && status !== 'all') {
      whereClause += ' AND o.status = ?';
      params.push(status);
    }
    
    if (user_id) {
      whereClause += ' AND o.user_id = ?';
      params.push(parseInt(user_id));
    }
    
    if (order_number) {
      whereClause += ' AND o.order_number LIKE ?';
      params.push(`%${order_number}%`);
    }
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM orders o ${whereClause}`;
    const [countResult] = await db.execute(countSql, params);
    const total = countResult[0].total;
    
    // 获取订单列表
    const sql = `
      SELECT 
        o.id, o.order_number, o.user_id, o.total_amount, o.status, o.created_at, o.updated_at,
        u.username, u.email,
        COUNT(oi.id) as item_count
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN order_items oi ON o.id = oi.order_id
      ${whereClause}
      GROUP BY o.id, o.order_number, o.user_id, o.total_amount, o.status, o.created_at, o.updated_at, u.username, u.email
      ORDER BY o.created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(limit), offset);
    const [orders] = await db.execute(sql, params);

    // 计算分页信息
    const totalPages = Math.ceil(total / parseInt(limit));
    
    success(res, {
      orders,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    }, '获取订单列表成功');

  } catch (err) {
    console.error('获取订单列表错误:', err);
    error(res, '获取订单列表失败', 500);
  }
}

/**
 * 获取订单详情
 */
async function getOrderDetail(req, res) {
  try {
    const { id } = req.params;
    
    // 获取订单基本信息
    const [orders] = await db.execute(
      `SELECT 
        o.id, o.order_number, o.user_id, o.total_amount, o.status, o.created_at, o.updated_at,
        u.username, u.email
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.id = ?`,
      [id]
    );

    if (orders.length === 0) {
      return error(res, '订单不存在', 404);
    }

    const order = orders[0];

    // 获取订单项目
    const [orderItems] = await db.execute(
      `SELECT 
        oi.id, oi.product_id, oi.quantity, oi.price,
        p.name as product_name, p.school_logo_url,
        pi.email_account, pi.email_password
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      LEFT JOIN product_inventory pi ON oi.inventory_id = pi.id
      WHERE oi.order_id = ?`,
      [id]
    );

    success(res, {
      order: {
        ...order,
        items: orderItems
      }
    }, '获取订单详情成功');

  } catch (err) {
    console.error('获取订单详情错误:', err);
    error(res, '获取订单详情失败', 500);
  }
}

/**
 * 更新订单状态
 */
async function updateOrderStatus(req, res) {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['pending_payment', 'paid', 'processing', 'completed', 'cancelled'];
    if (!status || !validStatuses.includes(status)) {
      return error(res, '订单状态无效', 400);
    }

    // 检查订单是否存在
    const [existingOrders] = await db.execute(
      'SELECT id, status FROM orders WHERE id = ?',
      [id]
    );

    if (existingOrders.length === 0) {
      return error(res, '订单不存在', 404);
    }

    const currentStatus = existingOrders[0].status;

    // 状态转换验证
    const statusTransitions = {
      'pending_payment': ['paid', 'cancelled'],
      'paid': ['processing', 'cancelled'],
      'processing': ['completed', 'cancelled'],
      'completed': [], // 已完成的订单不能再改变状态
      'cancelled': [] // 已取消的订单不能再改变状态
    };

    if (!statusTransitions[currentStatus].includes(status)) {
      return error(res, `订单状态不能从 ${currentStatus} 变更为 ${status}`, 400);
    }

    // 更新订单状态
    await db.execute(
      'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, id]
    );

    // 如果订单被取消，需要释放库存
    if (status === 'cancelled') {
      await db.execute(
        `UPDATE product_inventory pi 
         JOIN order_items oi ON pi.id = oi.inventory_id 
         SET pi.status = 'available' 
         WHERE oi.order_id = ? AND pi.status = 'sold'`,
        [id]
      );
    }

    // 获取更新后的订单信息
    const [updatedOrder] = await db.execute(
      `SELECT 
        o.id, o.order_number, o.user_id, o.total_amount, o.status, o.created_at, o.updated_at,
        u.username, u.email
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.id = ?`,
      [id]
    );

    success(res, {
      order: updatedOrder[0]
    }, '订单状态更新成功');

  } catch (err) {
    console.error('更新订单状态错误:', err);
    error(res, '更新订单状态失败', 500);
  }
}

/**
 * 获取订单统计
 */
async function getOrderStats(req, res) {
  try {
    const { period = 'today' } = req.query;
    
    let dateCondition = '';
    switch (period) {
      case 'today':
        dateCondition = 'DATE(created_at) = CURDATE()';
        break;
      case 'week':
        dateCondition = 'created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';
        break;
      case 'month':
        dateCondition = 'YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())';
        break;
      default:
        dateCondition = '1=1';
        break;
    }

    // 获取订单状态统计
    const [statusStats] = await db.execute(
      `SELECT 
        status,
        COUNT(*) as count,
        COALESCE(SUM(total_amount), 0) as total_amount
      FROM orders 
      WHERE ${dateCondition}
      GROUP BY status`
    );

    // 获取总体统计
    const [totalStats] = await db.execute(
      `SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_sales,
        COALESCE(AVG(total_amount), 0) as avg_order_value
      FROM orders 
      WHERE ${dateCondition}`
    );

    success(res, {
      period,
      status_stats: statusStats,
      total_stats: totalStats[0]
    }, '获取订单统计成功');

  } catch (err) {
    console.error('获取订单统计错误:', err);
    error(res, '获取订单统计失败', 500);
  }
}

/**
 * 删除订单
 */
async function deleteOrder(req, res) {
  try {
    const { id } = req.params;

    // 检查订单是否存在
    const [existingOrders] = await db.execute(
      'SELECT id, status FROM orders WHERE id = ?',
      [id]
    );

    if (existingOrders.length === 0) {
      return error(res, '订单不存在', 404);
    }

    // 只允许删除已取消或待支付的订单
    const allowedStatuses = ['cancelled', 'pending_payment'];
    if (!allowedStatuses.includes(existingOrders[0].status)) {
      return error(res, '只能删除已取消或待支付的订单', 400);
    }

    // 如果是待支付订单，需要释放库存
    if (existingOrders[0].status === 'pending_payment') {
      await db.execute(
        `UPDATE product_inventory pi 
         JOIN order_items oi ON pi.id = oi.inventory_id 
         SET pi.status = 'available' 
         WHERE oi.order_id = ? AND pi.status = 'reserved'`,
        [id]
      );
    }

    // 删除订单项目
    await db.execute('DELETE FROM order_items WHERE order_id = ?', [id]);

    // 删除订单
    await db.execute('DELETE FROM orders WHERE id = ?', [id]);

    success(res, null, '订单删除成功');

  } catch (err) {
    console.error('删除订单错误:', err);
    error(res, '删除订单失败', 500);
  }
}

module.exports = {
  getOrderList,
  getOrderDetail,
  updateOrderStatus,
  getOrderStats,
  deleteOrder
};
