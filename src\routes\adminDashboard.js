const express = require('express');
const router = express.Router();
const { adminAuth, logAdminAction } = require('../middleware/adminAuth');
const {
  getDashboardOverview,
  getChartData,
  getProductRanking,
  getInventoryAlerts,
  getUserStats
} = require('../controllers/adminDashboardController');

/**
 * @route   GET /api/admin/dashboard
 * @desc    获取仪表盘概览数据
 * @access  Private (Admin)
 */
router.get('/', adminAuth, logAdminAction('view', 'dashboard'), getDashboardOverview);

/**
 * @route   GET /api/admin/dashboard/charts
 * @desc    获取销售趋势图表数据
 * @access  Private (Admin)
 */
router.get('/charts', adminAuth, getChartData);

/**
 * @route   GET /api/admin/dashboard/products/ranking
 * @desc    获取商品销售排行
 * @access  Private (Admin)
 */
router.get('/products/ranking', adminAuth, getProductRanking);

/**
 * @route   GET /api/admin/dashboard/inventory/alerts
 * @desc    获取库存预警信息
 * @access  Private (Admin)
 */
router.get('/inventory/alerts', adminAuth, getInventoryAlerts);

/**
 * @route   GET /api/admin/dashboard/users/stats
 * @desc    获取用户统计信息
 * @access  Private (Admin)
 */
router.get('/users/stats', adminAuth, getUserStats);

module.exports = router;
