const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取系统设置
 */
async function getSettings(req, res) {
  try {
    const [settings] = await db.execute(
      'SELECT setting_key, setting_value, description FROM system_settings ORDER BY setting_key'
    );

    // 将设置转换为对象格式
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.setting_key] = {
        value: setting.setting_value,
        description: setting.description
      };
    });

    success(res, {
      settings: settingsObj
    }, '获取系统设置成功');

  } catch (err) {
    console.error('获取系统设置错误:', err);
    error(res, '获取系统设置失败', 500);
  }
}

/**
 * 更新系统设置
 */
async function updateSettings(req, res) {
  try {
    const { settings } = req.body;

    if (!settings || typeof settings !== 'object') {
      return error(res, '设置数据格式错误', 400);
    }

    // 开始事务
    await db.execute('START TRANSACTION');

    try {
      // 更新每个设置项
      for (const [key, value] of Object.entries(settings)) {
        await db.execute(
          `INSERT INTO system_settings (setting_key, setting_value) 
           VALUES (?, ?) 
           ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = CURRENT_TIMESTAMP`,
          [key, value]
        );
      }

      // 提交事务
      await db.execute('COMMIT');

      success(res, null, '系统设置更新成功');

    } catch (err) {
      // 回滚事务
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('更新系统设置错误:', err);
    error(res, '更新系统设置失败', 500);
  }
}

/**
 * 获取系统信息
 */
async function getSystemInfo(req, res) {
  try {
    // 获取数据库统计信息
    const [userCount] = await db.execute('SELECT COUNT(*) as count FROM users');
    const [productCount] = await db.execute('SELECT COUNT(*) as count FROM products');
    const [orderCount] = await db.execute('SELECT COUNT(*) as count FROM orders');
    const [inventoryCount] = await db.execute('SELECT COUNT(*) as count FROM product_inventory');

    // 获取系统运行时间等信息
    const systemInfo = {
      database_stats: {
        total_users: userCount[0].count,
        total_products: productCount[0].count,
        total_orders: orderCount[0].count,
        total_inventory: inventoryCount[0].count
      },
      server_info: {
        node_version: process.version,
        platform: process.platform,
        uptime: process.uptime(),
        memory_usage: process.memoryUsage()
      }
    };

    success(res, systemInfo, '获取系统信息成功');

  } catch (err) {
    console.error('获取系统信息错误:', err);
    error(res, '获取系统信息失败', 500);
  }
}

/**
 * 获取操作日志
 */
async function getAdminLogs(req, res) {
  try {
    const { 
      page = 1, 
      limit = 20, 
      admin_id = '',
      action = '',
      start_date = '',
      end_date = ''
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params = [];
    
    if (admin_id) {
      whereClause += ' AND al.admin_id = ?';
      params.push(parseInt(admin_id));
    }
    
    if (action) {
      whereClause += ' AND al.action LIKE ?';
      params.push(`%${action}%`);
    }
    
    if (start_date) {
      whereClause += ' AND DATE(al.created_at) >= ?';
      params.push(start_date);
    }
    
    if (end_date) {
      whereClause += ' AND DATE(al.created_at) <= ?';
      params.push(end_date);
    }
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM admin_logs al ${whereClause}`;
    const [countResult] = await db.execute(countSql, params);
    const total = countResult[0].total;
    
    // 获取日志列表
    const sql = `
      SELECT 
        al.id, al.admin_id, al.action, al.resource_type, al.resource_id, al.details, al.ip_address, al.created_at,
        a.username as admin_username
      FROM admin_logs al
      LEFT JOIN admins a ON al.admin_id = a.id
      ${whereClause}
      ORDER BY al.created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(limit), offset);
    const [logs] = await db.execute(sql, params);

    // 计算分页信息
    const totalPages = Math.ceil(total / parseInt(limit));
    
    success(res, {
      logs,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    }, '获取操作日志成功');

  } catch (err) {
    console.error('获取操作日志错误:', err);
    error(res, '获取操作日志失败', 500);
  }
}

/**
 * 清理系统数据
 */
async function cleanupSystem(req, res) {
  try {
    const { type } = req.body;

    if (!type) {
      return error(res, '清理类型不能为空', 400);
    }

    let cleanupResult = {};

    switch (type) {
      case 'logs':
        // 清理30天前的日志
        const [logResult] = await db.execute(
          'DELETE FROM admin_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)'
        );
        cleanupResult.deleted_logs = logResult.affectedRows;
        break;

      case 'cancelled_orders':
        // 清理已取消的订单（保留最近7天的）
        const [orderResult] = await db.execute(
          `DELETE FROM orders 
           WHERE status = 'cancelled' 
           AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)`
        );
        cleanupResult.deleted_orders = orderResult.affectedRows;
        break;

      case 'temp_data':
        // 清理临时数据（这里可以根据实际需求添加）
        cleanupResult.message = '临时数据清理完成';
        break;

      default:
        return error(res, '不支持的清理类型', 400);
    }

    success(res, cleanupResult, '系统清理完成');

  } catch (err) {
    console.error('系统清理错误:', err);
    error(res, '系统清理失败', 500);
  }
}

/**
 * 备份数据库
 */
async function backupDatabase(req, res) {
  try {
    // 这里应该实现数据库备份逻辑
    // 由于涉及到系统命令执行，这里只是一个示例
    const backupInfo = {
      backup_time: new Date().toISOString(),
      backup_file: `backup_${Date.now()}.sql`,
      status: 'completed'
    };

    success(res, backupInfo, '数据库备份完成');

  } catch (err) {
    console.error('数据库备份错误:', err);
    error(res, '数据库备份失败', 500);
  }
}

module.exports = {
  getSettings,
  updateSettings,
  getSystemInfo,
  getAdminLogs,
  cleanupSystem,
  backupDatabase
};
