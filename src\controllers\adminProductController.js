const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取商品列表
 */
async function getProductList(req, res) {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      status = 'all' 
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params = [];
    
    if (search) {
      whereClause += ' AND name LIKE ?';
      params.push(`%${search}%`);
    }
    
    if (status && status !== 'all') {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM products ${whereClause}`;
    const [countResult] = await db.execute(countSql, params);
    const total = countResult[0].total;
    
    // 获取商品列表
    const sql = `
      SELECT 
        id, name, school_logo_url, description, price, warranty_days, status, created_at, updated_at,
        (SELECT COUNT(*) FROM product_inventory WHERE product_id = products.id AND status = 'available') as available_stock,
        (SELECT COUNT(*) FROM product_inventory WHERE product_id = products.id AND status = 'sold') as sold_count
      FROM products 
      ${whereClause}
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(limit), offset);
    const [products] = await db.execute(sql, params);

    // 计算分页信息
    const totalPages = Math.ceil(total / parseInt(limit));
    
    success(res, {
      products,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    }, '获取商品列表成功');

  } catch (err) {
    console.error('获取商品列表错误:', err);
    error(res, '获取商品列表失败', 500);
  }
}

/**
 * 获取商品详情
 */
async function getProductDetail(req, res) {
  try {
    const { id } = req.params;
    
    const [products] = await db.execute(
      `SELECT 
        id, name, school_logo_url, description, price, warranty_days, status, created_at, updated_at,
        (SELECT COUNT(*) FROM product_inventory WHERE product_id = products.id AND status = 'available') as available_stock,
        (SELECT COUNT(*) FROM product_inventory WHERE product_id = products.id AND status = 'sold') as sold_count
      FROM products 
      WHERE id = ?`,
      [id]
    );

    if (products.length === 0) {
      return error(res, '商品不存在', 404);
    }

    success(res, {
      product: products[0]
    }, '获取商品详情成功');

  } catch (err) {
    console.error('获取商品详情错误:', err);
    error(res, '获取商品详情失败', 500);
  }
}

/**
 * 创建商品
 */
async function createProduct(req, res) {
  try {
    const { name, school_logo_url, description, price, warranty_days = 0 } = req.body;

    // 基础验证
    if (!name || !price) {
      return error(res, '商品名称和价格不能为空', 400);
    }

    if (parseFloat(price) <= 0) {
      return error(res, '商品价格必须大于0', 400);
    }

    // 创建商品
    const [result] = await db.execute(
      'INSERT INTO products (name, school_logo_url, description, price, warranty_days) VALUES (?, ?, ?, ?, ?)',
      [name, school_logo_url || null, description || null, parseFloat(price), parseInt(warranty_days)]
    );

    // 获取创建的商品信息
    const [newProduct] = await db.execute(
      'SELECT id, name, school_logo_url, description, price, warranty_days, status, created_at FROM products WHERE id = ?',
      [result.insertId]
    );

    success(res, {
      product: newProduct[0]
    }, '商品创建成功', 201);

  } catch (err) {
    console.error('创建商品错误:', err);
    error(res, '创建商品失败', 500);
  }
}

/**
 * 更新商品信息
 */
async function updateProduct(req, res) {
  try {
    const { id } = req.params;
    const { name, school_logo_url, description, price, warranty_days, status } = req.body;

    // 检查商品是否存在
    const [existingProducts] = await db.execute(
      'SELECT id FROM products WHERE id = ?',
      [id]
    );

    if (existingProducts.length === 0) {
      return error(res, '商品不存在', 404);
    }

    // 构建更新字段
    const updateFields = [];
    const updateParams = [];

    if (name) {
      updateFields.push('name = ?');
      updateParams.push(name);
    }

    if (school_logo_url !== undefined) {
      updateFields.push('school_logo_url = ?');
      updateParams.push(school_logo_url || null);
    }

    if (description !== undefined) {
      updateFields.push('description = ?');
      updateParams.push(description || null);
    }

    if (price) {
      if (parseFloat(price) <= 0) {
        return error(res, '商品价格必须大于0', 400);
      }
      updateFields.push('price = ?');
      updateParams.push(parseFloat(price));
    }

    if (warranty_days !== undefined) {
      updateFields.push('warranty_days = ?');
      updateParams.push(parseInt(warranty_days) || 0);
    }

    if (status && ['active', 'inactive'].includes(status)) {
      updateFields.push('status = ?');
      updateParams.push(status);
    }

    if (updateFields.length === 0) {
      return error(res, '没有需要更新的字段', 400);
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateParams.push(id);

    // 执行更新
    await db.execute(
      `UPDATE products SET ${updateFields.join(', ')} WHERE id = ?`,
      updateParams
    );

    // 获取更新后的商品信息
    const [updatedProduct] = await db.execute(
      'SELECT id, name, school_logo_url, description, price, warranty_days, status, created_at, updated_at FROM products WHERE id = ?',
      [id]
    );

    success(res, {
      product: updatedProduct[0]
    }, '商品信息更新成功');

  } catch (err) {
    console.error('更新商品信息错误:', err);
    error(res, '更新商品信息失败', 500);
  }
}

/**
 * 删除商品
 */
async function deleteProduct(req, res) {
  try {
    const { id } = req.params;

    // 检查商品是否存在
    const [existingProducts] = await db.execute(
      'SELECT id FROM products WHERE id = ?',
      [id]
    );

    if (existingProducts.length === 0) {
      return error(res, '商品不存在', 404);
    }

    // 检查是否有相关的库存
    const [inventory] = await db.execute(
      'SELECT id FROM product_inventory WHERE product_id = ?',
      [id]
    );

    if (inventory.length > 0) {
      return error(res, '商品有相关库存，无法删除', 400);
    }

    // 检查是否有相关的订单
    const [orderItems] = await db.execute(
      'SELECT id FROM order_items WHERE product_id = ?',
      [id]
    );

    if (orderItems.length > 0) {
      return error(res, '商品有相关订单，无法删除', 400);
    }

    // 删除商品
    await db.execute('DELETE FROM products WHERE id = ?', [id]);

    success(res, null, '商品删除成功');

  } catch (err) {
    console.error('删除商品错误:', err);
    error(res, '删除商品失败', 500);
  }
}

/**
 * 批量更新商品状态
 */
async function batchUpdateStatus(req, res) {
  try {
    const { product_ids, status } = req.body;

    if (!Array.isArray(product_ids) || product_ids.length === 0) {
      return error(res, '商品ID列表不能为空', 400);
    }

    if (!status || !['active', 'inactive'].includes(status)) {
      return error(res, '状态参数无效', 400);
    }

    // 批量更新商品状态
    const placeholders = product_ids.map(() => '?').join(',');
    const [result] = await db.execute(
      `UPDATE products SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id IN (${placeholders})`,
      [status, ...product_ids]
    );

    success(res, {
      updated_count: result.affectedRows
    }, `成功更新 ${result.affectedRows} 个商品状态`);

  } catch (err) {
    console.error('批量更新商品状态错误:', err);
    error(res, '批量更新商品状态失败', 500);
  }
}

module.exports = {
  getProductList,
  getProductDetail,
  createProduct,
  updateProduct,
  deleteProduct,
  batchUpdateStatus
};
