const bcrypt = require('bcryptjs');
const db = require('../config/database');
const { success, error, unauthorized } = require('../utils/response');

/**
 * 获取用户列表
 */
async function getUserList(req, res) {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      role = ''
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (search) {
      whereClause += ' AND (username LIKE ? OR email LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    if (role && role !== 'all') {
      whereClause += ' AND role = ?';
      params.push(role);
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const [countResult] = await db.execute(countSql, params);
    const total = countResult[0].total;

    // 获取用户列表 - 简化查询避免子查询问题
    const sql = `
      SELECT id, username, email, role, created_at, updated_at
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    // 为用户列表查询创建新的参数数组
    const listParams = [...params, parseInt(limit), offset];
    const [users] = await db.execute(sql, listParams);

    // 计算分页信息
    const totalPages = Math.ceil(total / parseInt(limit));

    success(res, {
      users,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    }, '获取用户列表成功');

  } catch (err) {
    console.error('获取用户列表错误:', err);
    error(res, '获取用户列表失败', 500);
  }
}

/**
 * 获取用户详情
 */
async function getUserDetail(req, res) {
  try {
    const { id } = req.params;

    const [users] = await db.execute(
      `SELECT
        id, username, email, role, created_at, updated_at,
        (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = users.id AND status = 'completed') as total_spent
      FROM users
      WHERE id = ?`,
      [id]
    );

    if (users.length === 0) {
      return error(res, '用户不存在', 404);
    }

    const user = users[0];

    // 获取用户最近的订单
    const [recentOrders] = await db.execute(
      `SELECT id, order_number, total_amount, status, created_at
       FROM orders
       WHERE user_id = ?
       ORDER BY created_at DESC
       LIMIT 5`,
      [id]
    );

    success(res, {
      user,
      recent_orders: recentOrders
    }, '获取用户详情成功');

  } catch (err) {
    console.error('获取用户详情错误:', err);
    error(res, '获取用户详情失败', 500);
  }
}

/**
 * 创建用户
 */
async function createUser(req, res) {
  try {
    const { username, email, password, role = 'customer' } = req.body;

    // 基础验证
    if (!username || !email || !password) {
      return error(res, '用户名、邮箱和密码不能为空', 400);
    }

    if (password.length < 6) {
      return error(res, '密码长度不能少于6位', 400);
    }

    if (!['customer', 'admin'].includes(role)) {
      return error(res, '用户角色无效', 400);
    }

    // 检查用户名是否已存在
    const [existingUsers] = await db.execute(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingUsers.length > 0) {
      return error(res, '用户名或邮箱已存在', 400);
    }

    // 加密密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const [result] = await db.execute(
      'INSERT INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)',
      [username, email, passwordHash, role]
    );

    // 获取创建的用户信息
    const [newUser] = await db.execute(
      'SELECT id, username, email, role, created_at FROM users WHERE id = ?',
      [result.insertId]
    );

    success(res, {
      user: newUser[0]
    }, '用户创建成功', 201);

  } catch (err) {
    console.error('创建用户错误:', err);
    error(res, '创建用户失败', 500);
  }
}

/**
 * 更新用户信息
 */
async function updateUser(req, res) {
  try {
    const { id } = req.params;
    const { username, email, role } = req.body;

    // 检查用户是否存在
    const [existingUsers] = await db.execute(
      'SELECT id FROM users WHERE id = ?',
      [id]
    );

    if (existingUsers.length === 0) {
      return error(res, '用户不存在', 404);
    }

    // 检查用户名和邮箱是否被其他用户使用
    if (username || email) {
      const [duplicateUsers] = await db.execute(
        'SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?',
        [username || '', email || '', id]
      );

      if (duplicateUsers.length > 0) {
        return error(res, '用户名或邮箱已被其他用户使用', 400);
      }
    }

    // 构建更新字段
    const updateFields = [];
    const updateParams = [];

    if (username) {
      updateFields.push('username = ?');
      updateParams.push(username);
    }

    if (email) {
      updateFields.push('email = ?');
      updateParams.push(email);
    }

    if (role && ['customer', 'admin'].includes(role)) {
      updateFields.push('role = ?');
      updateParams.push(role);
    }

    if (updateFields.length === 0) {
      return error(res, '没有需要更新的字段', 400);
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateParams.push(id);

    // 执行更新
    await db.execute(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateParams
    );

    // 获取更新后的用户信息
    const [updatedUser] = await db.execute(
      'SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ?',
      [id]
    );

    success(res, {
      user: updatedUser[0]
    }, '用户信息更新成功');

  } catch (err) {
    console.error('更新用户信息错误:', err);
    error(res, '更新用户信息失败', 500);
  }
}

/**
 * 重置用户密码
 */
async function resetUserPassword(req, res) {
  try {
    const { id } = req.params;
    const { new_password } = req.body;

    if (!new_password) {
      return error(res, '新密码不能为空', 400);
    }

    if (new_password.length < 6) {
      return error(res, '新密码长度不能少于6位', 400);
    }

    // 检查用户是否存在
    const [existingUsers] = await db.execute(
      'SELECT id FROM users WHERE id = ?',
      [id]
    );

    if (existingUsers.length === 0) {
      return error(res, '用户不存在', 404);
    }

    // 加密新密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(new_password, saltRounds);

    // 更新密码
    await db.execute(
      'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [passwordHash, id]
    );

    success(res, null, '用户密码重置成功');

  } catch (err) {
    console.error('重置用户密码错误:', err);
    error(res, '重置用户密码失败', 500);
  }
}

/**
 * 删除用户
 */
async function deleteUser(req, res) {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const [existingUsers] = await db.execute(
      'SELECT id, role FROM users WHERE id = ?',
      [id]
    );

    if (existingUsers.length === 0) {
      return error(res, '用户不存在', 404);
    }

    // 防止删除管理员账户（可选的安全措施）
    if (existingUsers[0].role === 'admin') {
      return error(res, '不能删除管理员账户', 403);
    }

    // 检查用户是否有未完成的订单
    const [pendingOrders] = await db.execute(
      "SELECT id FROM orders WHERE user_id = ? AND status = 'pending_payment'",
      [id]
    );

    if (pendingOrders.length > 0) {
      return error(res, '用户有未完成的订单，无法删除', 400);
    }

    // 删除用户
    await db.execute('DELETE FROM users WHERE id = ?', [id]);

    success(res, null, '用户删除成功');

  } catch (err) {
    console.error('删除用户错误:', err);
    error(res, '删除用户失败', 500);
  }
}

module.exports = {
  getUserList,
  getUserDetail,
  createUser,
  updateUser,
  resetUserPassword,
  deleteUser
};