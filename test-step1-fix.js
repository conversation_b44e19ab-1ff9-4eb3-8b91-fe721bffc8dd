/**
 * 第一步修复验证测试
 * 验证数据库结构、基础认证和核心API功能是否正常工作
 */

const request = require('supertest');
const app = require('./src/app');
const db = require('./src/config/database');

describe('第一步修复验证测试', () => {
  let adminToken;
  let testUserId;

  beforeAll(async () => {
    // 等待数据库连接
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  afterAll(async () => {
    // 清理测试数据
    if (testUserId) {
      try {
        await db.execute('DELETE FROM users WHERE id = ?', [testUserId]);
      } catch (err) {
        console.log('清理测试用户失败:', err.message);
      }
    }
    
    // 关闭数据库连接
    if (db && db.end) {
      await db.end();
    }
  });

  describe('1. 数据库结构验证', () => {
    test('应该能够连接到数据库', async () => {
      const [result] = await db.execute('SELECT 1 as test');
      expect(result[0].test).toBe(1);
    });

    test('admin_logs表应该存在', async () => {
      const [result] = await db.execute('SHOW TABLES LIKE "admin_logs"');
      expect(result.length).toBe(1);
    });

    test('admins表应该存在', async () => {
      const [result] = await db.execute('SHOW TABLES LIKE "admins"');
      expect(result.length).toBe(1);
    });

    test('users表应该存在', async () => {
      const [result] = await db.execute('SHOW TABLES LIKE "users"');
      expect(result.length).toBe(1);
    });

    test('products表应该存在', async () => {
      const [result] = await db.execute('SHOW TABLES LIKE "products"');
      expect(result.length).toBe(1);
    });

    test('orders表应该存在', async () => {
      const [result] = await db.execute('SHOW TABLES LIKE "orders"');
      expect(result.length).toBe(1);
    });

    test('product_inventory表应该存在', async () => {
      const [result] = await db.execute('SHOW TABLES LIKE "product_inventory"');
      expect(result.length).toBe(1);
    });
  });

  describe('2. 管理员认证功能验证', () => {
    test('应该能够进行管理员登录', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: 'admin',
          password: 'admin123'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('admin');
      
      adminToken = response.body.data.token;
    });

    test('应该拒绝无效的管理员登录', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: 'admin',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    test('应该能够验证管理员token', async () => {
      const response = await request(app)
        .get('/api/admin/auth/profile')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('admin');
    });
  });

  describe('3. 用户管理API验证', () => {
    test('应该能够获取用户列表（无SQL参数错误）', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('users');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.users)).toBe(true);
    });

    test('应该能够搜索用户（无SQL参数错误）', async () => {
      const response = await request(app)
        .get('/api/admin/users?search=test')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('应该能够按角色筛选用户（无SQL参数错误）', async () => {
      const response = await request(app)
        .get('/api/admin/users?role=customer')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('应该能够创建新用户', async () => {
      const response = await request(app)
        .post('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'testuser_step1',
          email: '<EMAIL>',
          password: 'password123',
          role: 'customer'
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      
      testUserId = response.body.data.user.id;
    });
  });

  describe('4. 仪表盘API验证', () => {
    test('应该能够获取仪表盘概览数据', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('total_users');
      expect(response.body.data).toHaveProperty('total_orders');
      expect(response.body.data).toHaveProperty('total_products');
    });

    test('应该能够获取图表数据（无SQL参数错误）', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/charts')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('sales_trend');
      expect(response.body.data).toHaveProperty('order_status');
    });

    test('应该能够获取商品销售排行（无SQL参数错误）', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/products/ranking')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('ranking');
      expect(Array.isArray(response.body.data.ranking)).toBe(true);
    });
  });

  describe('5. 商品管理API验证', () => {
    test('应该能够获取商品列表', async () => {
      const response = await request(app)
        .get('/api/admin/products')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('products');
      expect(Array.isArray(response.body.data.products)).toBe(true);
    });
  });

  describe('6. 库存管理API验证', () => {
    test('应该能够获取库存列表', async () => {
      const response = await request(app)
        .get('/api/admin/inventory')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('inventory');
      expect(Array.isArray(response.body.data.inventory)).toBe(true);
    });
  });

  describe('7. 订单管理API验证', () => {
    test('应该能够获取订单列表', async () => {
      const response = await request(app)
        .get('/api/admin/orders')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('orders');
      expect(Array.isArray(response.body.data.orders)).toBe(true);
    });
  });

  describe('8. 系统设置API验证', () => {
    test('应该能够获取系统设置', async () => {
      const response = await request(app)
        .get('/api/admin/settings')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('settings');
    });

    test('应该能够获取系统信息', async () => {
      const response = await request(app)
        .get('/api/admin/settings/system-info')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('database_stats');
      expect(response.body.data).toHaveProperty('server_info');
    });
  });
});
