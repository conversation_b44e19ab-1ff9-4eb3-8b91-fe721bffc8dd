const express = require('express');
const router = express.Router();
const { adminAuth, logAdminAction } = require('../middleware/adminAuth');
const {
  getInventoryList,
  updateInventory,
  adjustInventory,
  getInventoryHistory
} = require('../controllers/adminInventoryController');

/**
 * @route   GET /api/admin/inventory
 * @desc    获取库存列表
 * @access  Private (Admin)
 */
router.get('/', adminAuth, logAdminAction('view', 'inventory'), getInventoryList);



/**
 * @route   GET /api/admin/inventory/:id/history
 * @desc    获取库存历史
 * @access  Private (Admin)
 */
router.get('/:id/history', adminAuth, logAdminAction('view', 'inventory_history'), getInventoryHistory);

/**
 * @route   POST /api/admin/inventory/:id/adjust
 * @desc    调整库存
 * @access  Private (Admin)
 */
router.post('/:id/adjust', adminAuth, logAdminAction('adjust', 'inventory'), adjustInventory);

/**
 * @route   PUT /api/admin/inventory/:id
 * @desc    更新库存
 * @access  Private (Admin)
 */
router.put('/:id', adminAuth, logAdminAction('update', 'inventory_item'), updateInventory);



module.exports = router;
